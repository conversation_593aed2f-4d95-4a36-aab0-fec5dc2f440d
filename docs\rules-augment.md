# Rules Augment

When working on complex coding tasks, debugging issues, or multi-step implementations, use the sequential-thinking tool to break down problems systematically and think through solutions step-by-step. This is especially important for:

1. **Complex debugging scenarios** - Use sequential thinking to analyze error patterns, trace root causes, and develop systematic fix strategies
2. **Multi-component implementations** - Break down large features into logical steps and validate each component
3. **Integration challenges** - Think through how different parts of the codebase interact and identify potential conflicts
4. **Architecture decisions** - Analyze trade-offs and implications of different implementation approaches

Additionally, when you need information about external libraries, frameworks, or packages that aren't part of the current codebase, use the context7 tools to:

- Resolve library names to get accurate documentation
- Fetch up-to-date API references and usage patterns
- Understand best practices for external dependencies

Always start complex tasks with sequential thinking to plan your approach, then use context7 when you need external library documentation, and return to sequential thinking if you encounter unexpected issues or need to revise your strategy.

Also, allways call the mcp tool interactive-feedback when finish every step of the work,
