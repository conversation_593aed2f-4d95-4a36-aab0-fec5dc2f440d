/* Teams Link Generator Component Styling */

/* Section container styling */
.section-container {
  background-color: #ffffff;
  border: 1px solid #dddbda;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
}

.section-header {
  background-color: #f3f2f2;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #dddbda;
  border-radius: 0.25rem 0.25rem 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #080707;
  display: flex;
  align-items: center;
}

/* Teams Integration Styling */
.teams-status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;
  color: #0176d3;
  font-size: 0.75rem;
}

.teams-status-text {
  font-style: italic;
}

.teams-link-info {
  margin-top: 0.5rem;
}

.teams-link-card {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: #f3f9ff;
  border: 1px solid #0176d3;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.teams-success-icon {
  color: #04844b;
}

.teams-link-text {
  flex: 1;
  color: #0176d3;
  font-weight: 500;
}

.teams-test-button {
  background: transparent;
  border: none;
  color: #0176d3;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.teams-test-button:hover {
  background-color: rgba(1, 118, 211, 0.1);
}

.teams-actions {
  margin-top: 0.75rem;
  display: flex;
  justify-content: flex-start;
}

.teams-regenerate-button {
  font-size: 0.75rem;
  padding: 0.5rem 1rem;
}

/* Responsive design */
@media (max-width: 30em) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .teams-status-indicator {
    margin-left: 0;
  }
  
  .teams-link-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .teams-actions {
    justify-content: center;
  }
}