{"name": "salesforce-app", "private": true, "version": "1.0.0", "description": "Salesforce App", "scripts": {"lint": "eslint **/{aura,lwc}/**/*.js", "test": "npm run test:unit", "test:unit": "sfdx-lwc-jest", "test:unit:watch": "sfdx-lwc-jest --watch", "test:unit:debug": "sfdx-lwc-jest --debug", "test:unit:coverage": "sfdx-lwc-jest --coverage", "prettier": "prettier --write \"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}\"", "prettier:verify": "prettier --check \"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}\"", "postinstall": "husky init", "precommit": "lint-staged", "prepare": "husky"}, "devDependencies": {"@lwc/engine-dom": "^8.20.0", "@lwc/eslint-plugin-lwc": "^2.2.0", "@lwc/jest-preset": "^19.1.0", "@prettier/plugin-xml": "^3.4.1", "@salesforce/eslint-config-lwc": "^3.7.2", "@salesforce/eslint-plugin-aura": "^2.1.0", "@salesforce/eslint-plugin-lightning": "^1.0.1", "@salesforce/sfdx-lwc-jest": "^7.0.1", "eslint": "^8.57.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.13.5", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "prettier-plugin-apex": "^2.2.6"}, "lint-staged": {"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}": ["prettier --write"], "**/{aura,lwc}/**/*.js": ["eslint"]}}