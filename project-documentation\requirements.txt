# MkDocs Material Documentation Requirements
# Install with: pip install -r requirements.txt

# Core MkDocs
mkdocs>=1.5.0
mkdocs-material>=9.4.0

# Essential plugins
mkdocs-git-revision-date-localized-plugin>=1.2.0
mkdocs-git-committers-plugin-2>=1.2.0
mkdocs-minify-plugin>=0.7.0
mkdocs-mermaid2-plugin>=1.1.0

# Additional plugins for enhanced functionality
mkdocs-tags-plugin>=0.3.0
mkdocs-awesome-pages-plugin>=2.9.0
mkdocs-redirects>=1.2.0
mkdocs-macros-plugin>=1.0.0

# Python dependencies
Pygments>=2.16.0
pymdown-extensions>=10.3.0
python-markdown-math>=0.8
markdown>=3.5.0

# Optional but recommended
pillow>=10.0.0
cairosvg>=2.7.0
