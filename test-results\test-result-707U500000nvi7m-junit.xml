<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
    <testsuite name="force.apex" timestamp="2025-06-11T17:41:16.000Z" hostname="https://reino-capital.my.salesforce.com" tests="41" failures="0"  errors="0"  time="4.17">
        <properties>
            <property name="outcome" value="Successful"/>
            <property name="testsRan" value="41"/>
            <property name="passing" value="41"/>
            <property name="failing" value="0"/>
            <property name="skipped" value="0"/>
            <property name="passRate" value="100%"/>
            <property name="failRate" value="0%"/>
            <property name="testStartTime" value="Wed Jun 11 2025 14:41:16"/>
            <property name="testSetupTimeInMs" value="3704"/>
            <property name="testExecutionTime" value="4.17 s"/>
            <property name="testTotalTime" value="7.87 s"/>
            <property name="commandTime" value="0.34 s"/>
            <property name="hostname" value="https://reino-capital.my.salesforce.com"/>
            <property name="orgId" value="00DHp00000DynwYMAR"/>
            <property name="username" value="<EMAIL>"/>
            <property name="testRunId" value="707U500000nvi7m"/>
            <property name="userId" value="005Hp00000km90zIAA"/>
        </properties>
        <testcase name="testCreateAppointment_AllStatusValues" classname="AppointmentControllerTest" time="0.28">
        </testcase>
        <testcase name="testCreateAppointment_MissingData" classname="AppointmentControllerTest" time="0.03">
        </testcase>
        <testcase name="testCreateAppointment_Success" classname="AppointmentControllerTest" time="0.39">
        </testcase>
        <testcase name="testCreateAppointment_WithStatusReuniao" classname="AppointmentControllerTest" time="0.23">
        </testcase>
        <testcase name="testGenerateEventSubject" classname="AppointmentControllerTest" time="0.50">
        </testcase>
        <testcase name="testGenerateEventSubject_NullInput" classname="AppointmentControllerTest" time="0.03">
        </testcase>
        <testcase name="testGetAppointmentDetails_InvalidEventId" classname="AppointmentControllerTest" time="0.02">
        </testcase>
        <testcase name="testGetAppointmentDetails_NoEventId" classname="AppointmentControllerTest" time="0.02">
        </testcase>
        <testcase name="testGetAppointmentDetails_ReturnsStatusReuniao" classname="AppointmentControllerTest" time="0.15">
        </testcase>
        <testcase name="testGetAppointmentDetails_ValidEventId" classname="AppointmentControllerTest" time="0.18">
        </testcase>
        <testcase name="testGetAppointmentDetails_WhatIdOnly" classname="AppointmentControllerTest" time="0.08">
        </testcase>
        <testcase name="testGetAppointmentDetails_WhoIdOnly" classname="AppointmentControllerTest" time="0.07">
        </testcase>
        <testcase name="testGetContactInformation_Contact" classname="AppointmentControllerTest" time="0.06">
        </testcase>
        <testcase name="testGetContactInformation_Lead" classname="AppointmentControllerTest" time="0.08">
        </testcase>
        <testcase name="testGetOpportunityInformation_Account" classname="AppointmentControllerTest" time="0.07">
        </testcase>
        <testcase name="testGetOpportunityInformation_Opportunity" classname="AppointmentControllerTest" time="0.06">
        </testcase>
        <testcase name="testGetOptimalMeetingTimes" classname="AppointmentControllerTest" time="0.06">
        </testcase>
        <testcase name="testGetOptimalMeetingTimes_DefaultValues" classname="AppointmentControllerTest" time="0.07">
        </testcase>
        <testcase name="testGetOptimalMeetingTimes_InvalidParameters" classname="AppointmentControllerTest" time="0.02">
        </testcase>
        <testcase name="testGetOptimalMeetingTimes_InvalidParams" classname="AppointmentControllerTest" time="0.02">
        </testcase>
        <testcase name="testGetOptimalMeetingTimes_Success" classname="AppointmentControllerTest" time="0.06">
        </testcase>
        <testcase name="testGetUserAvailability_InvalidParameters" classname="AppointmentControllerTest" time="0.02">
        </testcase>
        <testcase name="testGetUserAvailability_Success" classname="AppointmentControllerTest" time="0.06">
        </testcase>
        <testcase name="testGetUserAvailability_WithConflicts" classname="AppointmentControllerTest" time="0.13">
        </testcase>
        <testcase name="testSearchContacts_EmptySearchTerm" classname="AppointmentControllerTest" time="0.02">
        </testcase>
        <testcase name="testSearchContacts_NullMaxResults" classname="AppointmentControllerTest" time="0.10">
        </testcase>
        <testcase name="testSearchContacts_ShortSearchTerm" classname="AppointmentControllerTest" time="0.02">
        </testcase>
        <testcase name="testSearchContacts_Success" classname="AppointmentControllerTest" time="0.08">
        </testcase>
        <testcase name="testSearchOpportunities_EmptySearchTerm" classname="AppointmentControllerTest" time="0.02">
        </testcase>
        <testcase name="testSearchOpportunities_NullMaxResults" classname="AppointmentControllerTest" time="0.08">
        </testcase>
        <testcase name="testSearchOpportunities_ShortSearchTerm" classname="AppointmentControllerTest" time="0.02">
        </testcase>
        <testcase name="testSearchOpportunities_Success" classname="AppointmentControllerTest" time="0.06">
        </testcase>
        <testcase name="testSearchUsers_NoSearchTerm" classname="AppointmentControllerTest" time="0.07">
        </testcase>
        <testcase name="testSearchUsers_NullMaxResults" classname="AppointmentControllerTest" time="0.07">
        </testcase>
        <testcase name="testSearchUsers_WithSearchTerm" classname="AppointmentControllerTest" time="0.05">
        </testcase>
        <testcase name="testUpdateAppointment_InvalidEventId" classname="AppointmentControllerTest" time="0.02">
        </testcase>
        <testcase name="testUpdateAppointment_NoEventId" classname="AppointmentControllerTest" time="0.02">
        </testcase>
        <testcase name="testUpdateAppointment_NullStatusReuniao" classname="AppointmentControllerTest" time="0.34">
        </testcase>
        <testcase name="testUpdateAppointment_Success" classname="AppointmentControllerTest" time="0.25">
        </testcase>
        <testcase name="testUpdateAppointment_WithStatusReuniao" classname="AppointmentControllerTest" time="0.21">
        </testcase>
    </testsuite>
</testsuites>
