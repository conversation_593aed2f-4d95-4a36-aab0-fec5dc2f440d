{"summary": {"failRate": "0%", "failing": 0, "hostname": "https://reino-capital.my.salesforce.com", "orgId": "00DHp00000DynwYMAR", "outcome": "Passed", "passRate": "100%", "passing": 41, "skipped": 0, "testRunId": "707U500000nvi7m", "testStartTime": "2025-06-11T17:41:16.000Z", "testsRan": 41, "userId": "005Hp00000km90zIAA", "username": "<EMAIL>", "commandTime": "340 ms", "testExecutionTime": "4166 ms", "testTotalTime": "7870 ms"}, "tests": [{"Id": "07MU5000001MpuYMAS", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testCreateAppointment_AllStatusValues", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 281, "FullName": "AppointmentControllerTest.testCreateAppointment_AllStatusValues"}, {"Id": "07MU5000001MpuZMAS", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testCreateAppointment_MissingData", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 31, "FullName": "AppointmentControllerTest.testCreateAppointment_MissingData"}, {"Id": "07MU5000001MpuaMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testCreateAppointment_Success", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 389, "FullName": "AppointmentControllerTest.testCreateAppointment_Success"}, {"Id": "07MU5000001MpubMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testCreateAppointment_WithStatusReuniao", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 233, "FullName": "AppointmentControllerTest.testCreateAppointment_WithStatusReuniao"}, {"Id": "07MU5000001MpucMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGenerateEventSubject", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 500, "FullName": "AppointmentControllerTest.testGenerateEventSubject"}, {"Id": "07MU5000001MpudMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGenerateEventSubject_NullInput", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 25, "FullName": "AppointmentControllerTest.testGenerateEventSubject_NullInput"}, {"Id": "07MU5000001MpueMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetAppointmentDetails_InvalidEventId", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 22, "FullName": "AppointmentControllerTest.testGetAppointmentDetails_InvalidEventId"}, {"Id": "07MU5000001MpufMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetAppointmentDetails_NoEventId", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 23, "FullName": "AppointmentControllerTest.testGetAppointmentDetails_NoEventId"}, {"Id": "07MU5000001MpugMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetAppointmentDetails_ReturnsStatusReuniao", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 153, "FullName": "AppointmentControllerTest.testGetAppointmentDetails_ReturnsStatusReuniao"}, {"Id": "07MU5000001MpuhMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetAppointmentDetails_ValidEventId", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 184, "FullName": "AppointmentControllerTest.testGetAppointmentDetails_ValidEventId"}, {"Id": "07MU5000001MpuiMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetAppointmentDetails_WhatIdOnly", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 83, "FullName": "AppointmentControllerTest.testGetAppointmentDetails_WhatIdOnly"}, {"Id": "07MU5000001MpujMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetAppointmentDetails_WhoIdOnly", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 73, "FullName": "AppointmentControllerTest.testGetAppointmentDetails_WhoIdOnly"}, {"Id": "07MU5000001MpukMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetContactInformation_Contact", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 56, "FullName": "AppointmentControllerTest.testGetContactInformation_Contact"}, {"Id": "07MU5000001MpulMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetContactInformation_Lead", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 81, "FullName": "AppointmentControllerTest.testGetContactInformation_Lead"}, {"Id": "07MU5000001MpumMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetOpportunityInformation_Account", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 71, "FullName": "AppointmentControllerTest.testGetOpportunityInformation_Account"}, {"Id": "07MU5000001MpunMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetOpportunityInformation_Opportunity", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 59, "FullName": "AppointmentControllerTest.testGetOpportunityInformation_Opportunity"}, {"Id": "07MU5000001MpuoMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetOptimalMeetingTimes", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 63, "FullName": "AppointmentControllerTest.testGetOptimalMeetingTimes"}, {"Id": "07MU5000001MpupMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetOptimalMeetingTimes_DefaultValues", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 69, "FullName": "AppointmentControllerTest.testGetOptimalMeetingTimes_DefaultValues"}, {"Id": "07MU5000001MpuqMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetOptimalMeetingTimes_InvalidParameters", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 22, "FullName": "AppointmentControllerTest.testGetOptimalMeetingTimes_InvalidParameters"}, {"Id": "07MU5000001MpurMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetOptimalMeetingTimes_InvalidParams", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 23, "FullName": "AppointmentControllerTest.testGetOptimalMeetingTimes_InvalidParams"}, {"Id": "07MU5000001MpusMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetOptimalMeetingTimes_Success", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 60, "FullName": "AppointmentControllerTest.testGetOptimalMeetingTimes_Success"}, {"Id": "07MU5000001MputMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetUserAvailability_InvalidParameters", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 23, "FullName": "AppointmentControllerTest.testGetUserAvailability_InvalidParameters"}, {"Id": "07MU5000001MpuuMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetUserAvailability_Success", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 55, "FullName": "AppointmentControllerTest.testGetUserAvailability_Success"}, {"Id": "07MU5000001MpuvMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testGetUserAvailability_WithConflicts", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 133, "FullName": "AppointmentControllerTest.testGetUserAvailability_WithConflicts"}, {"Id": "07MU5000001MpuwMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testSearchContacts_EmptySearchTerm", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 22, "FullName": "AppointmentControllerTest.testSearchContacts_EmptySearchTerm"}, {"Id": "07MU5000001MpuxMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testSearchContacts_NullMaxResults", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 105, "FullName": "AppointmentControllerTest.testSearchContacts_NullMaxResults"}, {"Id": "07MU5000001MpuyMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testSearchContacts_ShortSearchTerm", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 22, "FullName": "AppointmentControllerTest.testSearchContacts_ShortSearchTerm"}, {"Id": "07MU5000001MpuzMAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testSearchContacts_Success", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 78, "FullName": "AppointmentControllerTest.testSearchContacts_Success"}, {"Id": "07MU5000001Mpv0MAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testSearchOpportunities_EmptySearchTerm", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 23, "FullName": "AppointmentControllerTest.testSearchOpportunities_EmptySearchTerm"}, {"Id": "07MU5000001Mpv1MAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testSearchOpportunities_NullMaxResults", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 84, "FullName": "AppointmentControllerTest.testSearchOpportunities_NullMaxResults"}, {"Id": "07MU5000001Mpv2MAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testSearchOpportunities_ShortSearchTerm", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 23, "FullName": "AppointmentControllerTest.testSearchOpportunities_ShortSearchTerm"}, {"Id": "07MU5000001Mpv3MAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testSearchOpportunities_Success", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 62, "FullName": "AppointmentControllerTest.testSearchOpportunities_Success"}, {"Id": "07MU5000001Mpv4MAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testSearchUsers_NoSearchTerm", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 68, "FullName": "AppointmentControllerTest.testSearchUsers_NoSearchTerm"}, {"Id": "07MU5000001Mpv5MAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testSearchUsers_NullMaxResults", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 66, "FullName": "AppointmentControllerTest.testSearchUsers_NullMaxResults"}, {"Id": "07MU5000001Mpv6MAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testSearchUsers_WithSearchTerm", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 51, "FullName": "AppointmentControllerTest.testSearchUsers_WithSearchTerm"}, {"Id": "07MU5000001Mpv7MAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testUpdateAppointment_InvalidEventId", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 23, "FullName": "AppointmentControllerTest.testUpdateAppointment_InvalidEventId"}, {"Id": "07MU5000001Mpv8MAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testUpdateAppointment_NoEventId", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 23, "FullName": "AppointmentControllerTest.testUpdateAppointment_NoEventId"}, {"Id": "07MU5000001Mpv9MAC", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testUpdateAppointment_NullStatusReuniao", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 339, "FullName": "AppointmentControllerTest.testUpdateAppointment_NullStatusReuniao"}, {"Id": "07MU5000001MpvAMAS", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testUpdateAppointment_Success", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 253, "FullName": "AppointmentControllerTest.testUpdateAppointment_Success"}, {"Id": "07MU5000001MpvBMAS", "QueueItemId": "709U500000DvisrIAB", "StackTrace": null, "Message": null, "AsyncApexJobId": "707U500000nvi7mIAA", "MethodName": "testUpdateAppointment_WithStatusReuniao", "Outcome": "Pass", "ApexClass": {"Id": "01pU5000000OguBIAS", "Name": "AppointmentControllerTest", "NamespacePrefix": null}, "RunTime": 212, "FullName": "AppointmentControllerTest.testUpdateAppointment_WithStatusReuniao"}]}